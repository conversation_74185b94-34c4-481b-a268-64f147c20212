[build]
  command = "npm install --no-audit --no-fund && npm run build"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_CONFIG_PRODUCTION = "false"

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["mongodb", "bcryptjs", "jsonwebtoken", "backblaze-b2", "node-fetch", "ml-matrix", "natural", "sentiment", "stopword", "lodash", "redis", "node-cron", "compromise"]

# API requests to Netlify Functions are handled automatically
# No need for explicit redirects for /.netlify/functions/*

# Redirect all other routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Cache control for assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# CORS headers for functions
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type, Authorization"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' https://fonts.gstatic.com; object-src 'none'; media-src 'self' data: https:; frame-src 'none';"