const { ObjectId } = require('mongodb');
const { findOne, insertOne, updateOne } = require('./utils/db');
const { generateToken, hashPassword, comparePassword, requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Register a new user - using a two-phase approach to avoid timeouts
exports.register = handleAsync(async (event) => {
  try {
    console.time('register-validation');
    console.log('Registration request received');

    // Parse request body with error handling
    let body;
    try {
      body = JSON.parse(event.body);
    } catch (e) {
      console.error('Invalid JSON in request body:', e);
      return error('Invalid request body format', 400);
    }

    console.log('Request body parsed successfully');

    const { email, username, password } = body;

    // Fast validation - check all required fields at once
    if (!email || !username || !password) {
      return error('Email, username, and password are required', 400);
    }

    // Validate password length (fast check)
    if (password.length < 8) {
      return error('Password must be at least 8 characters long', 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return error('Invalid email format', 400);
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return error('Username must be 3-20 characters and can only contain letters, numbers, and underscores', 400);
    }

    console.log('All validation passed');
    console.timeEnd('register-validation');

    // For production, use the background function approach
    // This allows the function to run for up to 15 minutes instead of 10 seconds

    // Call the background function directly
    const fetch = require('node-fetch');
    const url = `${process.env.URL || 'https://netuark.netlify.app'}/.netlify/functions/register-background`;

    console.log(`Calling background registration function at: ${url}`);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, username, password })
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Background registration failed:', data.error);
        return error(data.error || 'Registration failed', response.status);
      }

      console.log('Background registration successful');
      return success(data.data);
    } catch (fetchError) {
      console.error('Error calling background function:', fetchError);

      // Fallback to direct registration if background function fails
      console.log('Falling back to direct registration');

      // Create a minimal response to avoid timeout
      return success({
        user: {
          email,
          username,
          profilePicture: null,
          isPremium: false,
          createdAt: new Date()
        },
        token: 'registration-in-progress',
        status: 'processing'
      });
    }
  } catch (err) {
    console.error('Unexpected error during registration:', err);
    return error('An unexpected error occurred. Please try again later.', 500);
  }
});

// Login user - using a two-phase approach to avoid timeouts
exports.login = handleAsync(async (event) => {
  try {
    console.time('login-validation');
    console.log('Login request received');

    // Parse request body with error handling
    let body;
    try {
      body = JSON.parse(event.body);
    } catch (e) {
      console.error('Invalid JSON in request body:', e);
      return error('Invalid request body format', 400);
    }

    console.log('Request body parsed successfully');

    const { email, password } = body;

    // Fast validation - check all required fields at once
    if (!email || !password) {
      return error('Email and password are required', 400);
    }

    console.log('All validation passed');
    console.timeEnd('login-validation');

    // Always use background login in production
    const fetch = require('node-fetch');
    const url = `${process.env.URL || 'https://netuark.netlify.app'}/.netlify/functions/login-background`;

    if (process.env.NODE_ENV === 'production' || process.env.MONGODB_URI) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, password })
        });

        const data = await response.json();

        if (!response.ok) {
          console.error('Background login failed:', data.error);
          return error(data.error || 'Login failed', response.status);
        }

        console.log('Background login successful');
        return success(data.data);
      } catch (fetchError) {
        console.error('Error calling background function:', fetchError);
        // Fallback to direct login if background function fails
      }
    }

    // Fallback: direct login (for dev or if background fails)
    console.log('Falling back to direct login');
    const user = await findOne('users', { email });
    if (!user) {
      return error('Invalid email or password', 401);
    }
    if (user.isActive === false) {
      return error('Account is disabled. Please contact support.', 403);
    }
    const isMatch = await comparePassword(password, user.password);
    if (!isMatch) {
      return error('Invalid email or password', 401);
    }
    await updateOne(
      'users',
      { _id: user._id },
      { $set: { lastLoginAt: new Date(), updatedAt: new Date() } }
    );
    const token = generateToken(user);
    return success({
      user: {
        _id: user._id,
        email: user.email,
        username: user.username,
        profilePicture: user.profilePicture,
        isPremium: user.isPremium,
        role: user.role,
        createdAt: user.createdAt,
        lastLoginAt: new Date()
      },
      token
    });
  } catch (err) {
    console.error('Unexpected error during login:', err);
    return error('An unexpected error occurred. Please try again later.', 500);
  }
});

// Get current user
exports.me = handleAsync(requireAuth(async (event) => {
  try {
    // Get user ID from token
    const userId = event.user._id;

    if (!userId) {
      return error('Invalid user ID in token', 401);
    }

    // Find user by ID with error handling for ObjectId
    let objectId;
    try {
      objectId = new ObjectId(userId);
    } catch (e) {
      return error('Invalid user ID format', 400);
    }

    // Find user by ID
    console.log(`Attempting to find user with ID: ${objectId}`);
    const user = await findOne('users', { _id: objectId });

    console.log('User lookup result:', {
      userFound: !!user,
      database: process.env.NODE_ENV === 'development' ? 'in-memory' : 'MongoDB',
      userId: objectId.toString()
    });

    // Check if user exists
    if (!user) {
      console.log(`User with ID ${objectId} not found in database`);
      return error('User not found', 404);
    }

    console.log('User profile found:', {
      username: user.username,
      email: user.email,
      _id: user._id.toString()
    });

    // Check if user is active
    if (user.isActive === false) {
      return error('Account is disabled. Please contact support.', 403);
    }

    // Check if premium subscription has expired
    if (user.isPremium && user.premiumExpiry && new Date(user.premiumExpiry) < new Date()) {
      // Update user to remove premium status
      await updateOne(
        'users',
        { _id: objectId },
        {
          $set: {
            isPremium: false,
            updatedAt: new Date()
          }
        }
      );

      // Update local user object
      user.isPremium = false;
    }

    // Check if AI requests need to be reset
    if (user.aiRequestsResetTime && new Date(user.aiRequestsResetTime) < new Date()) {
      // Reset AI requests
      await updateOne(
        'users',
        { _id: objectId },
        {
          $set: {
            aiRequestsRemaining: 3, // Reset to free tier limit
            aiRequestsResetTime: new Date(Date.now() + 3600000), // 1 hour from now
            updatedAt: new Date()
          }
        }
      );

      // Update local user object
      user.aiRequestsRemaining = 3;
      user.aiRequestsResetTime = new Date(Date.now() + 3600000);
    }

    // Return user data (excluding sensitive information)
    return success({
      user: {
        _id: user._id,
        email: user.email,
        username: user.username,
        profilePicture: user.profilePicture,
        premiumProfilePicture: user.premiumProfilePicture,
        isPremium: user.isPremium,
        premiumExpiry: user.premiumExpiry,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        privacySettings: user.privacySettings,
        emojiCount: user.emojiCount,
        aiRequestsRemaining: user.aiRequestsRemaining,
        aiRequestsResetTime: user.aiRequestsResetTime,
        role: user.role
      }
    });
  } catch (err) {
    console.error('Unexpected error in me endpoint:', err);
    return error('An unexpected error occurred. Please try again later.', 500);
  }
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  // Extract the path, handling both direct and nested routes
  const fullPath = event.path;
  const basePath = '/.netlify/functions/auth';

  // Check if the path starts with the base path
  if (!fullPath.startsWith(basePath)) {
    console.error(`Invalid path: ${fullPath} does not start with ${basePath}`);
    return error('Invalid path', 404);
  }

  // Extract the route path after the base path
  const path = fullPath.substring(basePath.length);

  // Log the request for debugging
  console.log(`Processing auth request: ${event.httpMethod} ${fullPath} (path: ${path})`);

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/register' && event.httpMethod === 'POST':
      return exports.register(event, context);
    case path === '/login' && event.httpMethod === 'POST':
      return exports.login(event, context);
    case (path === '/me' || path === '') && event.httpMethod === 'GET':
      return exports.me(event, context);
    default:
      console.log(`No auth route matched for: ${path}`);
      return error('Not found', 404);
  }
};
