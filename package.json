{"name": "netuark", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "gsap": "^3.12.0", "@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "mongodb": "^6.0.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "backblaze-b2": "^1.7.0", "node-fetch": "^2.7.0", "ml-matrix": "^6.10.0", "natural": "^6.12.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "stopword": "^2.0.8", "lodash": "^4.17.21", "redis": "^4.6.0", "node-cron": "^3.0.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.0.0", "@rollup/rollup-linux-arm64-gnu": "^4.0.0", "@rollup/rollup-darwin-x64": "^4.0.0", "@rollup/rollup-darwin-arm64": "^4.0.0", "@rollup/rollup-win32-x64-msvc": "^4.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}