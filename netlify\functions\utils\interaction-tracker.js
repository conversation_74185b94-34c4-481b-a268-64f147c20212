const { insertOne, updateOne, find, findOne } = require('./db');
const { ObjectId } = require('mongodb');

/**
 * User Interaction Tracking System
 * Tracks all user interactions for recommendation algorithm
 */
class InteractionTracker {
  constructor() {
    this.batchSize = 100;
    this.batchTimeout = 5000; // 5 seconds
    this.pendingInteractions = [];
    this.batchTimer = null;
  }

  /**
   * Track a user interaction with a post
   */
  async trackInteraction(userId, postId, type, metadata = {}) {
    try {
      const interaction = {
        userId: new ObjectId(userId),
        postId: new ObjectId(postId),
        type, // 'view', 'like', 'comment', 'share', 'save', 'click'
        metadata,
        createdAt: new Date(),
        sessionId: metadata.sessionId || null,
        deviceType: metadata.deviceType || 'unknown',
        duration: metadata.duration || null
      };

      // Add to batch for efficient processing
      this.pendingInteractions.push(interaction);

      // Process batch if it's full or start timer
      if (this.pendingInteractions.length >= this.batchSize) {
        await this.processBatch();
      } else if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => this.processBatch(), this.batchTimeout);
      }

      // For critical interactions, also update immediately
      if (['like', 'comment', 'share'].includes(type)) {
        await this.updateUserProfile(userId, postId, type);
      }

      return { success: true };
    } catch (error) {
      console.error('Error tracking interaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process batch of interactions
   */
  async processBatch() {
    if (this.pendingInteractions.length === 0) return;

    try {
      // Clear timer
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
      }

      // Get current batch
      const batch = [...this.pendingInteractions];
      this.pendingInteractions = [];

      // Insert all interactions
      if (batch.length > 0) {
        await this.insertInteractionsBatch(batch);
      }

      console.log(`Processed ${batch.length} interactions`);
    } catch (error) {
      console.error('Error processing interaction batch:', error);
      // Re-add failed interactions to queue
      this.pendingInteractions.unshift(...batch);
    }
  }

  /**
   * Insert interactions in batch
   */
  async insertInteractionsBatch(interactions) {
    try {
      // Check if collection exists, create if not
      await this.ensureCollectionExists();

      // Insert all interactions
      const result = await insertOne('user_interactions', interactions);
      
      return result;
    } catch (error) {
      console.error('Error inserting interaction batch:', error);
      throw error;
    }
  }

  /**
   * Ensure user_interactions collection exists
   */
  async ensureCollectionExists() {
    try {
      // Try to find one document to check if collection exists
      await findOne('user_interactions', {});
    } catch (error) {
      // Collection might not exist, that's okay
      console.log('User interactions collection will be created on first insert');
    }
  }

  /**
   * Update user profile based on interaction
   */
  async updateUserProfile(userId, postId, interactionType) {
    try {
      // Get post details for profile updates
      const post = await findOne('posts', { _id: new ObjectId(postId) });
      if (!post) return;

      // Update user's interaction summary
      const updateData = {
        $inc: {
          [`interactionCounts.${interactionType}`]: 1,
          'interactionCounts.total': 1
        },
        $set: {
          lastActivityAt: new Date()
        }
      };

      // Add to preferred tags if post has AI tags
      if (post.aiTags && post.aiTags.length > 0) {
        const tagUpdates = {};
        post.aiTags.forEach(tag => {
          tagUpdates[`tagPreferences.${tag}`] = { $inc: 1 };
        });
        
        if (Object.keys(tagUpdates).length > 0) {
          updateData.$inc = { ...updateData.$inc, ...tagUpdates };
        }
      }

      await updateOne('users', { _id: new ObjectId(userId) }, updateData, { upsert: false });

    } catch (error) {
      console.error('Error updating user profile:', error);
    }
  }

  /**
   * Track post view with duration
   */
  async trackView(userId, postId, duration = null, metadata = {}) {
    return this.trackInteraction(userId, postId, 'view', {
      ...metadata,
      duration,
      timestamp: new Date()
    });
  }

  /**
   * Track post like
   */
  async trackLike(userId, postId, metadata = {}) {
    return this.trackInteraction(userId, postId, 'like', metadata);
  }

  /**
   * Track post unlike
   */
  async trackUnlike(userId, postId, metadata = {}) {
    return this.trackInteraction(userId, postId, 'unlike', metadata);
  }

  /**
   * Track comment
   */
  async trackComment(userId, postId, commentId, metadata = {}) {
    return this.trackInteraction(userId, postId, 'comment', {
      ...metadata,
      commentId
    });
  }

  /**
   * Track share
   */
  async trackShare(userId, postId, shareMethod = 'unknown', metadata = {}) {
    return this.trackInteraction(userId, postId, 'share', {
      ...metadata,
      shareMethod
    });
  }

  /**
   * Track save/bookmark
   */
  async trackSave(userId, postId, metadata = {}) {
    return this.trackInteraction(userId, postId, 'save', metadata);
  }

  /**
   * Track click on post elements
   */
  async trackClick(userId, postId, element, metadata = {}) {
    return this.trackInteraction(userId, postId, 'click', {
      ...metadata,
      element
    });
  }

  /**
   * Get user interaction summary
   */
  async getUserInteractionSummary(userId, timeframe = '7d') {
    try {
      const timeframeDays = parseInt(timeframe.replace('d', ''));
      const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

      const interactions = await find('user_interactions', {
        userId: new ObjectId(userId),
        createdAt: { $gte: startDate }
      });

      const summary = {
        total: interactions.length,
        byType: {},
        byDay: {},
        averageSessionDuration: 0,
        mostActiveHour: 0
      };

      let totalDuration = 0;
      let durationCount = 0;
      const hourCounts = new Array(24).fill(0);

      interactions.forEach(interaction => {
        // Count by type
        summary.byType[interaction.type] = (summary.byType[interaction.type] || 0) + 1;

        // Count by day
        const day = interaction.createdAt.toISOString().split('T')[0];
        summary.byDay[day] = (summary.byDay[day] || 0) + 1;

        // Track duration
        if (interaction.duration) {
          totalDuration += interaction.duration;
          durationCount++;
        }

        // Track hour activity
        const hour = interaction.createdAt.getHours();
        hourCounts[hour]++;
      });

      // Calculate averages
      if (durationCount > 0) {
        summary.averageSessionDuration = totalDuration / durationCount;
      }

      // Find most active hour
      summary.mostActiveHour = hourCounts.indexOf(Math.max(...hourCounts));

      return summary;
    } catch (error) {
      console.error('Error getting user interaction summary:', error);
      return null;
    }
  }

  /**
   * Get trending posts based on recent interactions
   */
  async getTrendingPosts(timeframe = '24h', limit = 20) {
    try {
      const timeframeDays = timeframe === '24h' ? 1 : parseInt(timeframe.replace('d', ''));
      const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

      const trendingData = await find('user_interactions', [
        {
          $match: {
            createdAt: { $gte: startDate },
            type: { $in: ['like', 'comment', 'share', 'view'] }
          }
        },
        {
          $group: {
            _id: '$postId',
            totalInteractions: { $sum: 1 },
            likes: { $sum: { $cond: [{ $eq: ['$type', 'like'] }, 1, 0] } },
            comments: { $sum: { $cond: [{ $eq: ['$type', 'comment'] }, 1, 0] } },
            shares: { $sum: { $cond: [{ $eq: ['$type', 'share'] }, 1, 0] } },
            views: { $sum: { $cond: [{ $eq: ['$type', 'view'] }, 1, 0] } },
            uniqueUsers: { $addToSet: '$userId' }
          }
        },
        {
          $addFields: {
            uniqueUserCount: { $size: '$uniqueUsers' },
            trendingScore: {
              $add: [
                { $multiply: ['$likes', 3] },
                { $multiply: ['$comments', 5] },
                { $multiply: ['$shares', 7] },
                { $multiply: ['$views', 1] }
              ]
            }
          }
        },
        { $sort: { trendingScore: -1 } },
        { $limit: limit }
      ]);

      return trendingData;
    } catch (error) {
      console.error('Error getting trending posts:', error);
      return [];
    }
  }

  /**
   * Clean up old interactions (for performance)
   */
  async cleanupOldInteractions(daysToKeep = 90) {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
      
      const result = await deleteMany('user_interactions', {
        createdAt: { $lt: cutoffDate }
      });

      console.log(`Cleaned up ${result.deletedCount} old interactions`);
      return result;
    } catch (error) {
      console.error('Error cleaning up old interactions:', error);
      return null;
    }
  }
}

module.exports = { InteractionTracker };
