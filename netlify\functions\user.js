const { ObjectId } = require('mongodb');
const jwt = require('jsonwebtoken');
const { findOne, find, updateOne, connectToDatabase } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { uploadFile, deleteFile } = require('./utils/storage');

// Update user profile
exports.updateProfile = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { username, bio } = JSON.parse(event.body);

  // Validate input
  if (!username) {
    return error('Username is required');
  }

  // Check if username is already taken (by another user)
  const existingUser = await findOne('users', { username, _id: { $ne: new ObjectId(userId) } });
  if (existingUser) {
    return error('Username already taken');
  }

  // Update user
  const updateData = {
    username,
    bio: bio || '',
    updatedAt: new Date()
  };

  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $set: updateData }
  );

  // Return updated user data
  return success({ message: 'Profile updated successfully' });
}));

// Upload profile picture
exports.uploadProfilePicture = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { image, isPremiumAvatar } = JSON.parse(event.body);

  // Validate input
  if (!image || !image.data) {
    return error('Image data is required');
  }

  // Check if user is premium for premium avatar
  if (isPremiumAvatar) {
    const user = await findOne('users', { _id: new ObjectId(userId) });
    if (!user.isPremium) {
      return error('Premium subscription required for GIF/MP4 avatars', 403);
    }
  }

  // Decode base64 image
  const fileBuffer = Buffer.from(image.data, 'base64');

  // Generate unique filename
  const fileExtension = image.type.split('/')[1];
  const fileName = `profiles/${userId}${isPremiumAvatar ? '_premium' : ''}_${Date.now()}.${fileExtension}`;

  // Upload to B2
  const uploadResult = await uploadFile(fileBuffer, fileName, image.type);

  // Update user profile
  const updateField = isPremiumAvatar ? 'premiumProfilePicture' : 'profilePicture';

  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $set: { [updateField]: uploadResult.fileUrl, updatedAt: new Date() } }
  );

  // Return success with file URL
  return success({ fileUrl: uploadResult.fileUrl });
}));

// Update privacy settings
exports.updatePrivacySettings = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { privacySettings } = JSON.parse(event.body);

  // Validate input
  if (!privacySettings) {
    return error('Privacy settings are required');
  }

  // Update user privacy settings
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $set: { privacySettings, updatedAt: new Date() } }
  );

  // Return success
  return success({ message: 'Privacy settings updated successfully' });
}));

// Get user profile by username (no authentication required for public profiles)
exports.getProfile = async (event, context) => {
  try {
    console.log('getProfile called with path:', event.path);

    // Get username from path parameter or query parameter
    const path = event.path.replace('/.netlify/functions/user', '');
    let username;

    if (path.startsWith('/profile/')) {
      // Extract username from path: /profile/username
      username = path.replace('/profile/', '');
    } else {
      // Fallback to query parameter
      username = event.queryStringParameters?.username;
    }

    console.log('Extracted username:', username);

    // Validate input
    if (!username) {
      console.log('No username provided');
      return error('Username is required');
    }

    console.log('Attempting to find user:', username);

    // Find user by username
    const user = await findOne('users', { username });

    console.log('User found:', !!user);

    // Check if user exists
    if (!user) {
      console.log('User not found in database');
      return error('User not found', 404);
    }

    // Get additional stats
    const posts = await find('posts', { author: user._id });
    const postsCount = posts.length;
    const followersCount = user.followers ? user.followers.length : 0;
    const followingCount = user.following ? user.following.length : 0;

    console.log('User stats calculated:', { postsCount, followersCount, followingCount });

    // Check if current user is following this user (if authenticated)
    let isFollowing = false;
    try {
      // Try to get current user if token is provided
      const authHeader = event.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-strong-secret-key-for-production-use-only-netuark-2024');
        if (decoded && decoded.userId) {
          const currentUser = await findOne('users', { _id: new ObjectId(decoded.userId) });
          isFollowing = currentUser?.following?.includes(user._id.toString()) || false;
        }
      }
    } catch (authError) {
      // Authentication failed, but that's okay for public profiles
      console.log('Authentication failed for profile view, continuing as guest');
    }

    // Return public user data
    return success({
      user: {
        _id: user._id,
        username: user.username,
        displayName: user.displayName || user.username,
        bio: user.bio || '',
        profilePicture: user.profilePicture,
        premiumProfilePicture: user.isPremium ? user.premiumProfilePicture : null,
        isPremium: user.isPremium || false,
        isVerified: user.isVerified || false,
        createdAt: user.createdAt
      },
      postsCount,
      followersCount,
      followingCount,
      isFollowing
    });
  } catch (err) {
    console.error('Error in getProfile:', err);
    console.error('Error stack:', err.stack);
    console.error('Error message:', err.message);

    // Check if it's a database connection error
    if (err.message && err.message.includes('MONGODB_URI')) {
      return error('Database configuration error. Please check environment variables.', 503);
    }

    if (err.message && (err.message.includes('connection') || err.message.includes('timeout'))) {
      return error('Database connection failed. Please try again later.', 503);
    }

    return error(`Internal server error: ${err.message}`, 500);
  }
};

// Get user posts by username (no authentication required for public posts)
exports.getUserPosts = async (event, context) => {
  try {
    console.log('getUserPosts called with path:', event.path);

    // Get username from path parameter
    const path = event.path.replace('/.netlify/functions/user', '');
    let username;

    if (path.includes('/posts/')) {
      // Extract username from path: /posts/username
      username = path.replace('/posts/', '');
    }

    console.log('Extracted username for posts:', username);

    // Validate input
    if (!username) {
      console.log('No username provided for posts');
      return error('Username is required');
    }

    // Find user by username
    const user = await findOne('users', { username });

    // Check if user exists
    if (!user) {
      console.log('User not found for posts:', username);
      return error('User not found', 404);
    }

    // Get user's posts (only public posts for non-authenticated users)
    const posts = await find(
      'posts',
      {
        author: user._id,
        visibility: 'public' // Only show public posts
      }
    );

    // Sort posts by creation date (newest first)
    posts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Populate author info for each post
    const postsWithAuthor = posts.map(post => ({
      ...post,
      author: {
        _id: user._id,
        username: user.username,
        displayName: user.displayName || user.username,
        profilePicture: user.profilePicture,
        isPremium: user.isPremium || false,
        isVerified: user.isVerified || false
      }
    }));

    // Return posts
    return success({
      posts: postsWithAuthor
    });
  } catch (err) {
    console.error('Error in getUserPosts:', err);
    console.error('Error stack:', err.stack);
    console.error('Error message:', err.message);

    // Check if it's a database connection error
    if (err.message && err.message.includes('MONGODB_URI')) {
      return error('Database configuration error. Please check environment variables.', 503);
    }

    if (err.message && (err.message.includes('connection') || err.message.includes('timeout'))) {
      return error('Database connection failed. Please try again later.', 503);
    }

    return error(`Internal server error: ${err.message}`, 500);
  }
};

// Blacklist a user
exports.blacklistUser = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { targetUserId } = JSON.parse(event.body);

  // Validate input
  if (!targetUserId) {
    return error('Target user ID is required');
  }

  // Check if target user exists
  const targetUser = await findOne('users', { _id: new ObjectId(targetUserId) });
  if (!targetUser) {
    return error('Target user not found', 404);
  }

  // Add target user to blacklist
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $addToSet: { blacklistedUsers: targetUserId } }
  );

  // Return success
  return success({ message: 'User blacklisted successfully' });
}));

// Remove user from blacklist
exports.removeFromBlacklist = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { targetUserId } = JSON.parse(event.body);

  // Validate input
  if (!targetUserId) {
    return error('Target user ID is required');
  }

  // Remove target user from blacklist
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $pull: { blacklistedUsers: targetUserId } }
  );

  // Return success
  return success({ message: 'User removed from blacklist successfully' });
}));

// Get blacklisted users
exports.getBlacklistedUsers = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get user
  const user = await findOne('users', { _id: new ObjectId(userId) });

  if (!user) {
    return error('User not found', 404);
  }

  // Get blacklisted user IDs
  const blacklistedUserIds = user.blacklistedUsers || [];

  // Convert to ObjectId
  const objectIds = blacklistedUserIds.map(id => new ObjectId(id));

  // Get blacklisted users
  const blacklistedUsers = await find(
    'users',
    { _id: { $in: objectIds } },
    {
      projection: {
        _id: 1,
        username: 1,
        profilePicture: 1,
        isPremium: 1
      }
    }
  );

  // Return blacklisted users
  return success({ users: blacklistedUsers });
}));

// Get privacy settings
exports.getPrivacySettings = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get user
  const user = await findOne('users', { _id: new ObjectId(userId) });

  if (!user) {
    return error('User not found', 404);
  }

  // Get privacy settings
  const settings = user.privacySettings || {
    postVisibility: 'public',
    allowFollowWithoutView: false,
    allowMentions: true,
    allowDirectMessages: true
  };

  // Return privacy settings
  return success({ settings });
}));

// Follow a user
exports.followUser = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { targetUserId, viewContent } = JSON.parse(event.body);

  // Validate input
  if (!targetUserId) {
    return error('Target user ID is required');
  }

  // Check if target user exists
  const targetUser = await findOne('users', { _id: new ObjectId(targetUserId) });
  if (!targetUser) {
    return error('Target user not found', 404);
  }

  // Check if target user has blacklisted the current user
  if (targetUser.blacklistedUsers && targetUser.blacklistedUsers.includes(userId.toString())) {
    return error('You cannot follow this user', 403);
  }

  // Check if target user allows follow without view
  if (!viewContent && targetUser.privacySettings && !targetUser.privacySettings.allowFollowWithoutView) {
    return error('This user does not allow following without viewing content', 403);
  }

  // Add target user to following
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    {
      $addToSet: { following: targetUserId },
      $set: {
        [`followingSettings.${targetUserId}`]: { viewContent: !!viewContent },
        updatedAt: new Date()
      }
    }
  );

  // Add current user to target user's followers
  await updateOne(
    'users',
    { _id: new ObjectId(targetUserId) },
    {
      $addToSet: { followers: userId.toString() },
      $set: { updatedAt: new Date() }
    }
  );

  // Return success
  return success({ message: 'User followed successfully' });
}));

// Unfollow a user
exports.unfollowUser = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { targetUserId } = JSON.parse(event.body);

  // Validate input
  if (!targetUserId) {
    return error('Target user ID is required');
  }

  // Remove target user from following
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    {
      $pull: { following: targetUserId },
      $unset: { [`followingSettings.${targetUserId}`]: "" },
      $set: { updatedAt: new Date() }
    }
  );

  // Remove current user from target user's followers
  await updateOne(
    'users',
    { _id: new ObjectId(targetUserId) },
    {
      $pull: { followers: userId.toString() },
      $set: { updatedAt: new Date() }
    }
  );

  // Return success
  return success({ message: 'User unfollowed successfully' });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/user', '');

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/profile' && event.httpMethod === 'PUT':
      return exports.updateProfile(event, context);
    case path === '/profile-picture' && event.httpMethod === 'POST':
      return exports.uploadProfilePicture(event, context);
    case path === '/privacy' && event.httpMethod === 'PUT':
      return exports.updatePrivacySettings(event, context);
    case path === '/privacy' && event.httpMethod === 'GET':
      return exports.getPrivacySettings(event, context);
    case path === '/profile' && event.httpMethod === 'GET':
      return exports.getProfile(event, context);
    case path.startsWith('/profile/') && event.httpMethod === 'GET':
      return exports.getProfile(event, context);
    case path.startsWith('/posts/') && event.httpMethod === 'GET':
      return exports.getUserPosts(event, context);
    case path === '/blacklist' && event.httpMethod === 'POST':
      return exports.blacklistUser(event, context);
    case path === '/blacklist' && event.httpMethod === 'DELETE':
      return exports.removeFromBlacklist(event, context);
    case path === '/blacklist' && event.httpMethod === 'GET':
      return exports.getBlacklistedUsers(event, context);
    case path === '/follow' && event.httpMethod === 'POST':
      return exports.followUser(event, context);
    case path === '/unfollow' && event.httpMethod === 'POST':
      return exports.unfollowUser(event, context);
    default:
      return error('Not found', 404);
  }
};
