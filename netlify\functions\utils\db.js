const { MongoClient, ServerApiVersion, ObjectId } = require('mongodb');

// Cache connection between function invocations
let cachedDb = null;
let cachedClient = null;

// Track connection time for performance monitoring
let connectionStartTime = null;

async function connectToDatabase() {
  if (cachedDb) {
    console.log('Using cached database connection');
    return cachedDb;
  }

  // Check if MongoDB URI is available
  const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://soham:<EMAIL>/?retryWrites=true&w=majority&appName=netuark';

  console.log('Environment:', process.env.NODE_ENV);
  console.log('MongoDB URI exists:', !!process.env.MONGODB_URI);
  console.log('MongoDB URI (first 50 chars):', mongoUri ? mongoUri.substring(0, 50) + '...' : 'undefined');
  console.time('db-connection');

  if (!mongoUri) {
    console.error('MONGODB_URI environment variable is not set');
    throw new Error('MONGODB_URI environment variable is required. Please check your Netlify environment variables.');
  }



  // Production MongoDB connection with proper options
  const uri = mongoUri;

  // Connection options for production optimized for Netlify Functions
  const options = {
    serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
    },
    maxPoolSize: 10, // Increased pool size for better concurrency
    minPoolSize: 1, // Keep at least one connection in the pool
    connectTimeoutMS: 5000, // Connection timeout (5 seconds)
    socketTimeoutMS: 10000, // Socket timeout (10 seconds)
    serverSelectionTimeoutMS: 5000, // Server selection timeout
    heartbeatFrequencyMS: 10000, // Heartbeat frequency
    retryWrites: true, // Enable retry for write operations
    w: 'majority', // Write concern for data durability
  };

  try {
    // Start timing the connection
    connectionStartTime = Date.now();
    console.log('Connecting to MongoDB...');

    // Create a MongoClient with connection pooling
    const client = new MongoClient(uri, options);

    // Connect the client to the server with timeout handling and retries
    let retries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`MongoDB connection attempt ${attempt}/${retries}`);

        await Promise.race([
          client.connect(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('MongoDB connection timeout after 5 seconds')), 5000)
          )
        ]);

        // If we get here, connection was successful
        break;

      } catch (connectError) {
        lastError = connectError;
        console.error(`MongoDB connection attempt ${attempt} failed:`, connectError.message);

        if (attempt === retries) {
          console.error('All MongoDB connection attempts failed');
          throw new Error(`MongoDB connection failed after ${retries} attempts: ${connectError.message}`);
        }

        // Wait before retrying (exponential backoff)
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        console.log(`Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    // Verify connection by pinging (with timeout)
    try {
      await Promise.race([
        client.db("admin").command({ ping: 1 }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('MongoDB ping timeout')), 1500)
        )
      ]);
    } catch (pingError) {
      console.error('MongoDB ping timeout:', pingError);
      // Close the client to prevent connection leaks
      await client.close();
      throw new Error('MongoDB ping failed: ' + pingError.message);
    }

    const connectionTime = Date.now() - connectionStartTime;
    console.log(`Successfully connected to MongoDB Atlas in ${connectionTime}ms`);
    console.timeEnd('db-connection');

    // Get the database
    const db = client.db(process.env.DB_NAME || 'netuark');

    // Cache the database connection and client for reuse
    cachedDb = db;
    cachedClient = client;

    return db;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    console.timeEnd('db-connection');
    // Throw the error instead of falling back to in-memory database
    throw new Error(`MongoDB connection failed: ${error.message}`);
  }
}

// Helper function to execute database operations with timeout
async function executeWithTimeout(operation, timeoutMs = 2500, fallback = null) {
  try {
    return await Promise.race([
      operation(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Database operation timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  } catch (error) {
    console.error('Database operation error:', error);
    return fallback;
  }
}

// Helper functions for common database operations
async function findOne(collection, query, options = {}) {
  console.time(`db-findOne-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).findOne(query, options),
    2500,
    null
  );
  console.timeEnd(`db-findOne-${collection}`);
  return result;
}

async function find(collection, query, options = {}) {
  console.time(`db-find-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).find(query, options).toArray(),
    3000,
    []
  );
  console.timeEnd(`db-find-${collection}`);
  return result;
}

async function insertOne(collection, document) {
  console.time(`db-insertOne-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).insertOne(document),
    3000,
    { acknowledged: false }
  );
  console.timeEnd(`db-insertOne-${collection}`);
  return result;
}

async function updateOne(collection, filter, update, options = {}) {
  console.time(`db-updateOne-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).updateOne(filter, update, options),
    2500,
    { acknowledged: false, modifiedCount: 0 }
  );
  console.timeEnd(`db-updateOne-${collection}`);
  return result;
}

async function deleteOne(collection, filter) {
  console.time(`db-deleteOne-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).deleteOne(filter),
    2500,
    { acknowledged: false, deletedCount: 0 }
  );
  console.timeEnd(`db-deleteOne-${collection}`);
  return result;
}

async function deleteMany(collection, filter) {
  console.time(`db-deleteMany-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).deleteMany(filter),
    3000,
    { acknowledged: false, deletedCount: 0 }
  );
  console.timeEnd(`db-deleteMany-${collection}`);
  return result;
}

async function aggregate(collection, pipeline) {
  console.time(`db-aggregate-${collection}`);
  const db = await connectToDatabase();
  const result = await executeWithTimeout(
    () => db.collection(collection).aggregate(pipeline).toArray(),
    4000,
    []
  );
  console.timeEnd(`db-aggregate-${collection}`);
  return result;
}

// Utility function to check database status
async function checkDatabaseStatus() {
  try {
    const db = await connectToDatabase();
    const isInMemory = process.env.NODE_ENV === 'development' || !process.env.MONGODB_URI;

    return {
      connected: true,
      type: isInMemory ? 'in-memory' : 'MongoDB',
      collections: isInMemory ? Object.keys(db.collections || {}) : null,
      environment: process.env.NODE_ENV || 'unknown',
      mongodbUriExists: !!process.env.MONGODB_URI
    };
  } catch (error) {
    return {
      connected: false,
      error: error.message,
      environment: process.env.NODE_ENV || 'unknown',
      mongodbUriExists: !!process.env.MONGODB_URI
    };
  }
}

module.exports = {
  connectToDatabase,
  findOne,
  find,
  insertOne,
  updateOne,
  deleteOne,
  deleteMany,
  aggregate,
  checkDatabaseStatus
};
